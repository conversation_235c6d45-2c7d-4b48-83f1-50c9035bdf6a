<?php
/**
 * Test script to verify new noindex implementation for specific URL patterns
 * Tests the new patterns: /destinations/north_america and /destinations/*_holidays/testimonials
 */

echo "Testing new noindex patterns for Bon Voyage website...\n\n";

// Base URL - adjust if testing on different environment
$baseUrl = 'https://bon-voyage.ddev.site'; // Local development
// $baseUrl = 'https://www.bon-voyage.co.uk'; // Production

// URLs to test - new patterns that should have noindex
$testUrls = [
    // North America destination (should have noindex)
    $baseUrl . '/destinations/north_america',
    
    // Testimonials under destinations ending with _holidays (should have noindex)
    $baseUrl . '/destinations/usa_holidays/testimonials',
    $baseUrl . '/destinations/canada_holidays/testimonials',
    $baseUrl . '/destinations/california_holidays/testimonials',
    $baseUrl . '/destinations/new_york_holidays/testimonials',
    $baseUrl . '/destinations/florida_holidays/testimonials',
    
    // Control URLs (should NOT have noindex)
    $baseUrl . '/destinations/usa_holidays', // Destination page itself
    $baseUrl . '/testimonials', // Main testimonials index
    $baseUrl . '/destinations', // Destinations index
];

function checkNoIndex($url) {
    echo "Testing: $url\n";
    
    // Get the HTML content
    $context = stream_context_create([
        'http' => [
            'timeout' => 10,
            'user_agent' => 'Mozilla/5.0 (compatible; NoindexTester/1.0)',
            'ignore_errors' => true
        ]
    ]);
    
    $html = @file_get_contents($url, false, $context);
    
    if ($html === false) {
        echo "  ❌ Could not fetch URL\n\n";
        return false;
    }
    
    // Check for noindex meta tag
    $hasNoIndex = false;
    $hasNoFollow = false;
    
    // Look for robots meta tag with noindex
    if (preg_match('/<meta\s+name=["\']robots["\']\s+content=["\']([^"\']*)["\'][^>]*>/i', $html, $matches)) {
        $robotsContent = strtolower($matches[1]);
        $hasNoIndex = strpos($robotsContent, 'noindex') !== false;
        $hasNoFollow = strpos($robotsContent, 'nofollow') !== false;
        
        echo "  🤖 Robots meta tag found: " . $matches[1] . "\n";
    } else {
        echo "  ℹ️  No robots meta tag found\n";
    }
    
    // Check HTTP headers for X-Robots-Tag
    $headers = get_headers($url, 1);
    if (isset($headers['X-Robots-Tag'])) {
        echo "  📡 X-Robots-Tag header: " . $headers['X-Robots-Tag'] . "\n";
        $headerContent = strtolower($headers['X-Robots-Tag']);
        $hasNoIndex = $hasNoIndex || strpos($headerContent, 'noindex') !== false;
        $hasNoFollow = $hasNoFollow || strpos($headerContent, 'nofollow') !== false;
    }
    
    // Determine expected behavior based on URL patterns
    $shouldHaveNoIndex = (
        strpos($url, '/destinations/north_america') !== false ||  // North America destination
        preg_match('/\/destinations\/[a-z_]+_holidays\/testimonials/', $url)  // *_holidays/testimonials pattern
    );
    
    // Report results
    if ($shouldHaveNoIndex) {
        if ($hasNoIndex) {
            echo "  ✅ CORRECT: Has noindex (as expected)\n";
            return true;
        } else {
            echo "  ❌ INCORRECT: Missing noindex (should have noindex)\n";
            return false;
        }
    } else {
        if ($hasNoIndex) {
            echo "  ❌ INCORRECT: Has noindex (should NOT have noindex)\n";
            return false;
        } else {
            echo "  ✅ CORRECT: No noindex (as expected)\n";
            return true;
        }
    }
}

// Run tests
$totalCount = 0;
$correctCount = 0;

foreach ($testUrls as $url) {
    $totalCount++;
    if (checkNoIndex($url)) {
        $correctCount++;
    }
    echo "\n";
}

echo "Results: $correctCount/$totalCount URLs configured correctly\n";

if ($correctCount === $totalCount) {
    echo "🎉 All URLs are correctly configured!\n";
} else {
    echo "⚠️  Some URLs need attention. Check the implementation.\n";
}

// Implementation notes
echo "\n=== NEW IMPLEMENTATION NOTES ===\n";
echo "✅ Added noindex logic to DestinationsController->view() for 'north_america' slug\n";
echo "✅ Added noindex logic to TestimonialsController->index() for destinations ending with '_holidays'\n";
echo "✅ Uses existing \$noIndex property and meta_tags.ctp infrastructure\n";
echo "\nThe implementation uses 'noindex, follow' which allows following links but prevents indexing.\n";
?>
