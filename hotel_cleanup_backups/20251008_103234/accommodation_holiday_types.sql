/*M!999999\- enable the sandbox mode */ 
-- <PERSON><PERSON><PERSON> dump 10.19  Distrib 10.11.9-MariaDB, for debian-linux-gnu (aarch64)
--
-- Host: db    Database: db
-- ------------------------------------------------------
-- Server version	10.11.9-MariaDB-ubu2204-log

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Dumping routines for database 'db'
--

--
-- Table structure for table `accommodation_holiday_types`
--

DROP TABLE IF EXISTS `accommodation_holiday_types`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `accommodation_holiday_types` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `accommodation_id` int(10) unsigned NOT NULL,
  `holiday_type_id` int(10) unsigned NOT NULL,
  `order` int(10) unsigned DEFAULT NULL,
  `featured` tinyint(1) unsigned NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`),
  KEY `destination_id` (`holiday_type_id`),
  KEY `accommodation_id` (`accommodation_id`)
) ENGINE=MyISAM AUTO_INCREMENT=838 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `accommodation_holiday_types`
--

LOCK TABLES `accommodation_holiday_types` WRITE;
/*!40000 ALTER TABLE `accommodation_holiday_types` DISABLE KEYS */;
INSERT INTO `accommodation_holiday_types` (`id`, `accommodation_id`, `holiday_type_id`, `order`, `featured`) VALUES (14,39,10,16,0),
(25,120,10,19,0),
(27,137,10,20,0),
(35,37,12,21,0),
(36,39,12,13,0),
(37,50,12,22,0),
(45,116,12,24,0),
(49,135,12,28,0),
(50,137,12,29,0),
(51,150,12,30,0),
(52,159,12,31,0),
(53,167,12,32,0),
(55,184,12,9,0),
(58,188,10,21,0),
(59,190,10,22,0),
(60,191,10,23,0),
(61,189,10,24,0),
(62,192,10,25,0),
(63,193,10,26,0),
(64,194,10,27,0),
(65,195,10,28,0),
(66,196,10,29,0),
(67,177,12,1,0),
(69,193,12,33,0),
(70,194,12,34,0),
(71,195,12,35,0),
(73,213,12,36,0),
(74,218,12,37,0),
(75,199,12,38,0),
(76,164,12,39,0),
(77,164,10,30,0),
(81,217,10,32,0),
(82,208,10,33,0),
(90,294,12,40,0),
(91,300,12,8,0),
(93,451,10,34,0),
(95,167,10,35,0),
(96,452,18,12,0),
(99,453,18,8,0),
(100,454,18,10,0),
(101,455,18,6,0),
(102,456,18,11,0),
(103,457,18,0,0),
(105,459,18,1,0),
(108,462,18,2,0),
(109,463,18,3,0),
(110,464,18,5,0),
(112,466,18,13,0),
(113,467,18,15,0),
(114,4,18,4,0),
(118,267,18,7,0),
(121,265,18,9,0),
(133,19,10,36,0),
(136,393,8,9,0),
(145,526,12,41,0),
(146,526,10,37,0),
(147,527,12,42,0),
(148,527,10,38,0),
(149,528,10,39,0),
(150,528,12,43,0),
(152,530,10,40,0),
(154,531,12,44,0),
(159,540,12,45,0),
(161,547,10,41,0),
(166,553,12,46,0),
(167,554,12,10,0),
(168,554,10,42,0),
(169,555,10,43,0),
(170,555,12,47,0),
(172,557,12,48,0),
(173,559,10,44,0),
(174,560,12,49,0),
(175,563,12,50,0),
(177,503,8,10,0),
(184,506,10,45,0),
(185,506,12,51,0),
(188,569,12,11,0),
(190,573,12,12,0),
(192,586,8,11,0),
(195,636,8,12,0),
(196,630,8,13,0),
(197,630,10,46,0),
(198,624,8,14,0),
(199,624,12,16,0),
(203,604,12,52,0),
(209,601,19,9,0),
(216,595,12,53,0),
(217,595,2,0,0),
(218,271,12,54,0),
(219,271,10,47,0),
(221,592,12,55,0),
(226,558,12,56,0),
(228,557,2,1,0),
(229,554,2,2,0),
(231,565,12,57,0),
(232,541,19,10,0),
(236,593,12,58,0),
(243,526,19,11,0),
(244,555,19,12,0),
(248,213,2,3,0),
(249,499,19,13,0),
(257,481,19,14,0),
(258,481,10,48,0),
(259,467,19,15,0),
(260,451,19,16,0),
(261,430,12,17,0),
(263,424,10,49,0),
(264,421,19,17,0),
(265,418,10,50,0),
(266,417,10,51,0),
(267,416,19,18,0),
(268,416,10,52,0),
(269,413,12,59,0),
(270,413,8,15,0),
(271,412,10,53,0),
(272,410,12,60,0),
(273,410,8,16,0),
(275,409,12,61,0),
(276,408,12,62,0),
(277,408,8,17,0),
(278,408,10,54,0),
(279,408,2,4,0),
(280,406,12,63,0),
(285,395,8,20,0),
(286,391,12,65,0),
(287,389,8,21,0),
(288,388,12,66,0),
(289,387,12,67,0),
(290,387,2,5,0),
(291,386,10,55,0),
(292,386,12,68,0),
(293,386,2,6,0),
(295,384,12,69,0),
(297,367,12,70,0),
(298,367,2,7,0),
(299,365,19,19,0),
(300,352,10,56,0),
(301,334,19,20,0),
(303,305,12,71,0),
(304,305,2,8,0),
(305,294,10,57,0),
(306,294,2,9,0),
(307,292,12,72,0),
(308,284,10,58,0),
(309,284,12,73,0),
(310,283,10,59,0),
(311,283,12,74,0),
(312,274,12,75,0),
(313,274,2,10,0),
(314,275,2,11,0),
(315,277,12,76,0),
(316,269,10,60,0),
(317,268,10,61,0),
(318,253,19,21,0),
(326,219,10,62,0),
(327,219,2,12,0),
(328,219,12,77,0),
(329,212,19,22,0),
(330,191,19,23,0),
(331,189,19,24,0),
(332,184,8,22,0),
(333,154,2,13,0),
(337,119,19,25,0),
(346,92,19,26,0),
(351,55,19,27,0),
(358,203,19,28,0),
(359,435,12,78,0),
(360,437,8,23,0),
(361,437,2,14,0),
(362,437,12,79,0),
(363,438,2,15,0),
(364,438,12,80,0),
(366,218,2,16,0),
(378,50,19,29,0),
(381,326,16,2,0),
(382,543,16,3,0),
(385,538,16,4,0),
(387,482,16,5,0),
(388,313,16,6,0),
(434,525,12,20,0),
(435,525,10,13,0),
(437,202,10,4,0),
(452,653,8,8,0),
(453,653,12,6,0),
(467,649,10,5,0),
(468,649,19,4,0),
(506,485,16,1,0),
(507,485,10,10,0),
(508,485,19,3,0),
(515,486,16,0,0),
(516,486,10,9,0),
(517,486,19,5,0),
(526,570,8,6,0),
(527,570,12,15,0),
(570,655,12,3,0),
(573,3,8,7,0),
(574,3,10,14,0),
(592,317,19,8,0),
(593,660,10,11,0),
(594,660,19,1,0),
(595,556,10,7,0),
(596,556,19,7,0),
(598,17,19,6,0),
(599,17,12,14,0),
(601,659,10,12,0),
(602,659,19,2,0),
(603,9,10,6,0),
(604,9,12,7,0),
(606,22,10,8,0),
(607,22,12,25,0),
(612,185,12,0,0),
(659,397,8,2,0),
(660,397,10,3,0),
(665,183,12,4,0),
(666,183,8,1,0),
(728,670,8,3,0),
(729,670,10,2,0),
(741,671,8,24,0),
(742,672,8,25,0),
(743,672,2,17,0),
(748,678,8,27,0),
(749,678,16,9,0),
(757,686,16,11,0),
(758,686,9,0,0),
(759,686,13,0,0),
(764,693,8,29,0),
(766,695,8,30,0),
(773,704,8,32,0),
(775,710,8,33,0),
(776,710,9,3,0),
(777,710,1,2,0),
(779,722,8,34,0),
(787,733,8,35,0),
(792,672,26,16,1),
(794,767,26,1,0),
(795,765,26,14,0),
(797,631,26,3,0),
(800,759,26,13,0),
(803,756,26,5,0),
(804,776,26,12,0),
(805,772,26,15,0),
(806,774,26,2,1),
(808,761,26,7,0),
(812,778,26,10,1),
(814,779,26,18,1),
(817,768,26,11,0),
(820,781,8,36,0),
(823,783,26,0,0),
(827,785,26,9,0),
(828,786,26,19,0),
(830,704,12,81,0),
(831,837,12,2,0),
(832,824,12,82,0),
(833,817,12,64,0),
(834,855,12,18,0),
(835,985,2,18,0),
(836,985,8,37,0),
(837,1014,12,83,0);
/*!40000 ALTER TABLE `accommodation_holiday_types` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-10-08 10:32:34
