#!/bin/bash
# Restore script for hotel cleanup backup
# Usage: ./restore.sh

set -e

DB_HOST=${DB_HOST:-"localhost"}
DB_USER=${DB_USER:-"root"}
DB_PASS=${DB_PASS:-""}
DB_NAME=${DB_NAME:-"bon_voyage"}

echo "WARNING: This will restore accommodation data and may overwrite existing data!"
echo "Database: $DB_HOST/$DB_NAME"
echo ""
read -p "Are you sure you want to continue? (yes/no): " confirm

if [ "$confirm" != "yes" ]; then
    echo "Restore cancelled."
    exit 0
fi

echo "Restoring accommodation data..."
mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" "$DB_NAME" < complete_accommodation_backup.sql

echo "Restore completed successfully!"
