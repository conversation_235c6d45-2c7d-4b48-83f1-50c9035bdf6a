<?php
/**
 * Hotel Cleanup Script
 *
 * Safely removes hotels (accommodations) from the system while preserving
 * specified hotels and properly cleaning up all relationships.
 *
 * Usage:
 *   php HotelCleanupScript.php --keep-ids="819,818" --dry-run
 *   php HotelCleanupScript.php --keep-ids="819,818" --execute
 *
 * Features:
 * - Dry-run mode for safe testing
 * - Comprehensive relationship cleanup
 * - Detailed logging and reporting
 * - Backup recommendations
 */

class HotelCleanupScript {

    private $mysqli;
    private $dryRun = true;
    private $keepIds = [];
    private $logFile;
    private $stats = [
        'accommodations_to_remove' => 0,
        'accommodations_to_keep' => 0,
        'relationships_to_clean' => 0,
        'content_blocks_to_remove' => 0,
        'images_to_unlink' => 0
    ];

    public function __construct() {
        $this->connectDatabase();
        $this->logFile = 'hotel_cleanup_' . date('Y-m-d_H-i-s') . '.log';
        $this->log("Hotel Cleanup Script initialized");
    }

    private function connectDatabase() {
        // Database connection - using DDEV settings from .env
        $host = 'db';
        $username = 'db';
        $password = 'db';
        $database = 'db';

        $this->mysqli = new mysqli($host, $username, $password, $database);

        if ($this->mysqli->connect_error) {
            die("Connection failed: " . $this->mysqli->connect_error);
        }

        $this->mysqli->set_charset("utf8");
    }

    private function log($message) {
        $timestamp = date('Y-m-d H:i:s');
        $logMessage = "[$timestamp] $message\n";
        echo $logMessage;
        file_put_contents($this->logFile, $logMessage, FILE_APPEND);
    }

    public function setKeepIds($ids) {
        $this->keepIds = array_map('intval', explode(',', $ids));
        $this->log("Hotels to keep: " . implode(', ', $this->keepIds));
    }

    public function setDryRun($dryRun) {
        $this->dryRun = $dryRun;
        $this->log("Mode: " . ($dryRun ? "DRY RUN (no changes will be made)" : "EXECUTE (changes will be applied)"));
    }

    public function analyze() {
        $this->log("\n=== ANALYSIS PHASE ===");

        // Get all accommodations
        $query = "SELECT id, name, slug FROM accommodation ORDER BY name";
        $result = $this->mysqli->query($query);

        $allAccommodations = [];
        $accommodationsToRemove = [];

        while ($row = $result->fetch_assoc()) {
            $allAccommodations[] = $row;
            if (!in_array($row['id'], $this->keepIds)) {
                $accommodationsToRemove[] = $row;
            }
        }

        $this->stats['accommodations_to_keep'] = count($this->keepIds);
        $this->stats['accommodations_to_remove'] = count($accommodationsToRemove);

        $this->log("Total accommodations: " . count($allAccommodations));
        $this->log("Accommodations to keep: " . $this->stats['accommodations_to_keep']);
        $this->log("Accommodations to remove: " . $this->stats['accommodations_to_remove']);

        // Show hotels to keep
        $this->log("\nHotels to KEEP:");
        foreach ($allAccommodations as $hotel) {
            if (in_array($hotel['id'], $this->keepIds)) {
                $this->log("  - {$hotel['id']}: {$hotel['name']} ({$hotel['slug']})");
            }
        }

        // Show first 10 hotels to remove
        $this->log("\nHotels to REMOVE (showing first 10):");
        $count = 0;
        foreach ($accommodationsToRemove as $hotel) {
            if ($count++ >= 10) {
                $remaining = count($accommodationsToRemove) - 10;
                $this->log("  ... and $remaining more");
                break;
            }
            $this->log("  - {$hotel['id']}: {$hotel['name']} ({$hotel['slug']})");
        }

        // Analyze relationships
        $this->analyzeRelationships($accommodationsToRemove);

        return $accommodationsToRemove;
    }

    private function analyzeRelationships($accommodationsToRemove) {
        $this->log("\n=== RELATIONSHIP ANALYSIS ===");

        $removeIds = array_column($accommodationsToRemove, 'id');
        $removeIdsStr = implode(',', $removeIds);

        if (empty($removeIds)) {
            $this->log("No accommodations to remove - skipping relationship analysis");
            return;
        }

        // Check each relationship table
        $relationships = [
            'accommodation_destinations' => 'accommodation_id',
            'accommodation_holiday_types' => 'accommodation_id',
            'accommodation_images' => 'accommodation_id',
            'landing_pages_accommodations' => 'accommodation_id'
        ];

        foreach ($relationships as $table => $column) {
            $query = "SELECT COUNT(*) as count FROM $table WHERE $column IN ($removeIdsStr)";
            $result = $this->mysqli->query($query);
            $count = $result->fetch_assoc()['count'];
            $this->log("$table: $count records to remove");
            $this->stats['relationships_to_clean'] += $count;
        }

        // Check content blocks
        $query = "SELECT COUNT(*) as count FROM content_blocks WHERE model = 'Accommodation' AND modelid IN ($removeIdsStr)";
        $result = $this->mysqli->query($query);
        $this->stats['content_blocks_to_remove'] = $result->fetch_assoc()['count'];
        $this->log("content_blocks: {$this->stats['content_blocks_to_remove']} records to remove");

        $this->log("Total relationship records to clean: " . $this->stats['relationships_to_clean']);
    }

    public function execute($accommodationsToRemove) {
        if ($this->dryRun) {
            $this->log("\n=== DRY RUN - NO CHANGES WILL BE MADE ===");
            $this->log("To execute changes, run with --execute flag");
            return;
        }

        $this->log("\n=== EXECUTION PHASE ===");
        $this->log("WARNING: Making actual changes to database!");

        $removeIds = array_column($accommodationsToRemove, 'id');
        $removeIdsStr = implode(',', $removeIds);

        if (empty($removeIds)) {
            $this->log("No accommodations to remove");
            return;
        }

        // Start transaction
        $this->mysqli->begin_transaction();

        try {
            // Remove relationships first
            $this->cleanupRelationships($removeIdsStr);

            // Remove content blocks
            $this->cleanupContentBlocks($removeIdsStr);

            // Finally remove accommodations
            $this->removeAccommodations($removeIdsStr);

            // Commit transaction
            $this->mysqli->commit();
            $this->log("SUCCESS: All changes committed successfully");

        } catch (Exception $e) {
            $this->mysqli->rollback();
            $this->log("ERROR: Transaction rolled back - " . $e->getMessage());
            throw $e;
        }
    }

    private function cleanupRelationships($removeIdsStr) {
        $relationships = [
            'accommodation_destinations' => 'accommodation_id',
            'accommodation_holiday_types' => 'accommodation_id',
            'accommodation_images' => 'accommodation_id',
            'landing_pages_accommodations' => 'accommodation_id'
        ];

        foreach ($relationships as $table => $column) {
            $query = "DELETE FROM $table WHERE $column IN ($removeIdsStr)";
            $result = $this->mysqli->query($query);
            $affected = $this->mysqli->affected_rows;
            $this->log("Removed $affected records from $table");
        }
    }

    private function cleanupContentBlocks($removeIdsStr) {
        $query = "DELETE FROM content_blocks WHERE model = 'Accommodation' AND modelid IN ($removeIdsStr)";
        $result = $this->mysqli->query($query);
        $affected = $this->mysqli->affected_rows;
        $this->log("Removed $affected content blocks");
    }

    private function removeAccommodations($removeIdsStr) {
        $query = "DELETE FROM accommodation WHERE id IN ($removeIdsStr)";
        $result = $this->mysqli->query($query);
        $affected = $this->mysqli->affected_rows;
        $this->log("Removed $affected accommodations");
    }

    public function printSummary() {
        $this->log("\n=== SUMMARY ===");
        foreach ($this->stats as $key => $value) {
            $this->log(ucwords(str_replace('_', ' ', $key)) . ": $value");
        }
        $this->log("Log file: " . $this->logFile);
    }
}

// CLI execution
if (php_sapi_name() === 'cli') {
    $options = getopt('', ['keep-ids:', 'dry-run', 'execute', 'help']);

    if (isset($options['help']) || !isset($options['keep-ids'])) {
        echo "Hotel Cleanup Script\n";
        echo "Usage: php HotelCleanupScript.php --keep-ids=\"819,818\" [--dry-run|--execute]\n\n";
        echo "Options:\n";
        echo "  --keep-ids     Comma-separated list of hotel IDs to keep\n";
        echo "  --dry-run      Analyze only, make no changes (default)\n";
        echo "  --execute      Actually perform the cleanup\n";
        echo "  --help         Show this help message\n\n";
        echo "IMPORTANT: Always run with --dry-run first!\n";
        echo "IMPORTANT: Create a database backup before using --execute!\n";
        exit(0);
    }

    $script = new HotelCleanupScript();
    $script->setKeepIds($options['keep-ids']);
    $script->setDryRun(!isset($options['execute']));

    $accommodationsToRemove = $script->analyze();
    $script->execute($accommodationsToRemove);
    $script->printSummary();

} else {
    echo "This script must be run from the command line.\n";
}
?>
