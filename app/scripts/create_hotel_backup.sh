#!/bin/bash
# Hotel Cleanup Backup Script
# Creates targeted backups of accommodation-related data before cleanup

set -e  # Exit on any error

# Configuration - using DDEV settings
DB_HOST=${DB_HOST:-"db"}
DB_USER=${DB_USER:-"db"}
DB_PASS=${DB_PASS:-"db"}
DB_NAME=${DB_NAME:-"db"}

BACKUP_DIR="./hotel_cleanup_backups/$(date +%Y%m%d_%H%M%S)"
LOG_FILE="$BACKUP_DIR/backup.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

warn() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1" | tee -a "$LOG_FILE"
    exit 1
}

# Create backup directory
mkdir -p "$BACKUP_DIR"

log "Starting Hotel Cleanup Backup"
log "Database: $DB_HOST/$DB_NAME"
log "Backup Directory: $BACKUP_DIR"

# Test database connection
mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" -e "SELECT 1;" "$DB_NAME" > /dev/null 2>&1 || error "Cannot connect to database"

log "Database connection successful"

# Backup accommodation-related tables
TABLES=(
    "accommodation"
    "accommodation_destinations"
    "accommodation_holiday_types"
    "accommodation_images"
    "landing_pages_accommodations"
)

log "Backing up accommodation tables..."
for table in "${TABLES[@]}"; do
    log "Backing up table: $table"
    mysqldump -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" \
        --single-transaction \
        --routines \
        --triggers \
        --complete-insert \
        "$DB_NAME" "$table" > "$BACKUP_DIR/${table}.sql" || error "Failed to backup $table"

    # Get row count
    count=$(mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" -N -e "SELECT COUNT(*) FROM $table;" "$DB_NAME")
    log "  - $count rows backed up"
done

# Backup content blocks for accommodations
log "Backing up accommodation content blocks..."
mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" -e "
    SELECT * FROM content_blocks
    WHERE model = 'Accommodation'
    INTO OUTFILE '$BACKUP_DIR/accommodation_content_blocks.csv'
    FIELDS TERMINATED BY ','
    ENCLOSED BY '\"'
    LINES TERMINATED BY '\n';
" "$DB_NAME" 2>/dev/null || warn "Could not export content blocks to CSV (may need FILE privileges)"

# Alternative content blocks backup using mysqldump
mysqldump -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" \
    --single-transaction \
    --complete-insert \
    --where="model = 'Accommodation'" \
    "$DB_NAME" "content_blocks" > "$BACKUP_DIR/accommodation_content_blocks.sql" || error "Failed to backup content blocks"

content_blocks_count=$(mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" -N -e "SELECT COUNT(*) FROM content_blocks WHERE model = 'Accommodation';" "$DB_NAME")
log "Content blocks backed up: $content_blocks_count rows"

# Create a combined backup file
log "Creating combined backup file..."
cat "$BACKUP_DIR"/*.sql > "$BACKUP_DIR/complete_accommodation_backup.sql"

# Create restore script
log "Creating restore script..."
cat > "$BACKUP_DIR/restore.sh" << 'EOF'
#!/bin/bash
# Restore script for hotel cleanup backup
# Usage: ./restore.sh

set -e

DB_HOST=${DB_HOST:-"localhost"}
DB_USER=${DB_USER:-"root"}
DB_PASS=${DB_PASS:-""}
DB_NAME=${DB_NAME:-"bon_voyage"}

echo "WARNING: This will restore accommodation data and may overwrite existing data!"
echo "Database: $DB_HOST/$DB_NAME"
echo ""
read -p "Are you sure you want to continue? (yes/no): " confirm

if [ "$confirm" != "yes" ]; then
    echo "Restore cancelled."
    exit 0
fi

echo "Restoring accommodation data..."
mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" "$DB_NAME" < complete_accommodation_backup.sql

echo "Restore completed successfully!"
EOF

chmod +x "$BACKUP_DIR/restore.sh"

# Generate backup summary
log "Generating backup summary..."
cat > "$BACKUP_DIR/backup_summary.txt" << EOF
Hotel Cleanup Backup Summary
============================
Created: $(date)
Database: $DB_HOST/$DB_NAME
Backup Directory: $BACKUP_DIR

Tables Backed Up:
EOF

for table in "${TABLES[@]}"; do
    count=$(mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" -N -e "SELECT COUNT(*) FROM $table;" "$DB_NAME")
    echo "- $table: $count rows" >> "$BACKUP_DIR/backup_summary.txt"
done

echo "- content_blocks (Accommodation): $content_blocks_count rows" >> "$BACKUP_DIR/backup_summary.txt"

cat >> "$BACKUP_DIR/backup_summary.txt" << EOF

Files Created:
- complete_accommodation_backup.sql (combined backup)
- restore.sh (restore script)
- backup_summary.txt (this file)
- backup.log (detailed log)

To restore: cd $BACKUP_DIR && ./restore.sh
EOF

# Calculate backup size
backup_size=$(du -sh "$BACKUP_DIR" | cut -f1)
log "Backup completed successfully!"
log "Backup size: $backup_size"
log "Backup location: $BACKUP_DIR"
log "To restore: cd $BACKUP_DIR && ./restore.sh"

echo ""
echo "=== BACKUP SUMMARY ==="
cat "$BACKUP_DIR/backup_summary.txt"
