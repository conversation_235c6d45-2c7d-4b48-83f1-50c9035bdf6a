# Hotel Cleanup Guide

This guide provides the safest approach to remove hotels (accommodations) from the Bon Voyage system while preserving specified hotels.

## ⚠️ IMPORTANT SAFETY NOTES

1. **ALWAYS create a backup before making any changes**
2. **ALWAYS run in dry-run mode first**
3. **Test on a staging environment if possible**
4. **Have a rollback plan ready**

## Files Created

- `HotelCleanupScript.php` - Main cleanup script with dry-run capability
- `create_hotel_backup.sh` - Creates targeted backups of accommodation data
- `HOTEL_CLEANUP_GUIDE.md` - This guide

## Database Relationships Handled

The script safely handles these relationships:

1. **accommodation_destinations** - Hotel-to-destination links
2. **accommodation_holiday_types** - Hotel-to-holiday-type links  
3. **accommodation_images** - Hotel image associations
4. **landing_pages_accommodations** - Landing page associations
5. **content_blocks** - Content blocks for hotels (model='Accommodation')
6. **accommodation** - The main hotel records

## Step-by-Step Process

### Step 1: Create Backup

```bash
# Create a comprehensive backup of all accommodation-related data
./app/scripts/create_hotel_backup.sh
```

This creates:
- Individual table backups
- Combined backup file
- Restore script
- Backup summary

### Step 2: Dry Run Analysis

```bash
# Analyze what would be removed (NO CHANGES MADE)
php app/scripts/HotelCleanupScript.php --keep-ids="819,818" --dry-run
```

Replace `819,818` with your actual hotel IDs to keep.

This will show you:
- Hotels that will be kept
- Hotels that will be removed
- Relationship records that will be cleaned up
- Content blocks that will be removed

### Step 3: Review the Analysis

Check the log file created by the dry run:
- Verify the hotels to keep are correct
- Review the hotels to be removed
- Confirm the relationship cleanup counts look reasonable

### Step 4: Execute the Cleanup (DANGER ZONE)

⚠️ **ONLY after you're satisfied with the dry-run results:**

```bash
# Actually perform the cleanup
php app/scripts/HotelCleanupScript.php --keep-ids="819,818" --execute
```

## Example Usage

```bash
# 1. Create backup
./app/scripts/create_hotel_backup.sh

# 2. Dry run to see what would happen
php app/scripts/HotelCleanupScript.php --keep-ids="819,818,820,821" --dry-run

# 3. Review the output and log file

# 4. If satisfied, execute the cleanup
php app/scripts/HotelCleanupScript.php --keep-ids="819,818,820,821" --execute
```

## Script Options

### HotelCleanupScript.php

- `--keep-ids="819,818"` - Comma-separated list of hotel IDs to preserve
- `--dry-run` - Analyze only, make no changes (default)
- `--execute` - Actually perform the cleanup
- `--help` - Show help message

### create_hotel_backup.sh

Environment variables (optional):
- `DB_HOST` - Database host (default: localhost)
- `DB_USER` - Database user (default: root)  
- `DB_PASS` - Database password (default: empty)
- `DB_NAME` - Database name (default: bon_voyage)

## What Gets Removed

For each hotel NOT in your keep list:

1. **Relationship records** in:
   - accommodation_destinations
   - accommodation_holiday_types
   - accommodation_images
   - landing_pages_accommodations

2. **Content blocks** where model='Accommodation'

3. **The hotel record** itself from accommodation table

## What Gets Preserved

- Hotels with IDs in your `--keep-ids` list
- All their relationships and content
- Images (the image files themselves are not deleted, only the associations)
- All other data (destinations, itineraries, etc.)

## Rollback Process

If something goes wrong:

```bash
# Navigate to your backup directory
cd hotel_cleanup_backups/[timestamp]/

# Run the restore script
./restore.sh
```

## Troubleshooting

### Common Issues

1. **Database connection errors**
   - Check database credentials in the script
   - Ensure MySQL is running
   - Verify database name

2. **Permission errors**
   - Ensure the script has write permissions for log files
   - Check MySQL user has DELETE privileges

3. **Large dataset timeouts**
   - The script uses transactions for safety
   - For very large datasets, consider removing hotels in batches

### Log Files

Each run creates a detailed log file:
- `hotel_cleanup_[timestamp].log` - Detailed execution log
- Check this file for any errors or warnings

## Database Configuration

Update these settings in `HotelCleanupScript.php` if needed:

```php
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'bon_voyage';
```

## Testing Recommendations

1. **Test on a copy** of your database first
2. **Start with a small subset** of hotels to remove
3. **Verify the kept hotels** still work correctly
4. **Check the website** to ensure no broken links

## Support

If you encounter issues:
1. Check the log files for detailed error messages
2. Ensure you have database backups
3. Test the restore process before proceeding
4. Consider running on smaller batches if you have many hotels

Remember: **Safety first!** Always backup and test before making changes to production data.
