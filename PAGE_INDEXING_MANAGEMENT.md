# Page Indexing Management Guide

This document explains how to control which pages are indexed by search engines on the Bon Voyage website using the existing noindex infrastructure.

## 🎯 Overview

The Bon Voyage website uses a simple and effective system to control page indexing through meta tags. The system automatically handles different environments and provides an easy way to mark specific pages as non-indexable.

## 🛠️ How It Works

### The Infrastructure

The noindex system consists of three main components:

1. **Base Controller** (`app/base_controller.php`) - Contains the `$noIndex` property
2. **Meta Tags Element** (`app/views/elements/chrome/meta_tags.ctp`) - Outputs the robots meta tag
3. **Individual Controllers** - Set the `$noIndex` property as needed

### Environment Behavior

- **Development Environment** (non-www.bon-voyage.co.uk): All pages get `noindex, nofollow`
- **Production Environment**: 
  - Pages with `$noIndex = true`: Get `noindex, follow`
  - Pages with `$noIndex = false`: No robots restrictions (fully indexable)

## 📝 How to Add Noindex to Pages

### Method 1: Controller-Level Implementation

Add noindex logic directly in the controller action:

```php
// In any controller action
function myAction() {
    // Your existing logic here...
    
    // Set noindex for this page
    $this->noIndex = true;
    
    // Continue with your logic...
}
```

### Method 2: Conditional Noindex

Add conditional logic based on URL patterns or data:

```php
// Example: Noindex based on URL pattern
function view() {
    // Your existing logic...
    
    if (isset($this->params['destination_slug'])) {
        $destinationSlug = $this->params['destination_slug'];
        
        // Noindex for specific patterns
        if ($destinationSlug === 'north_america') {
            $this->noIndex = true;
        }
    }
    
    // Continue with your logic...
}
```

### Method 3: Data-Driven Noindex

Base noindex decision on database content:

```php
// Example: Noindex based on data properties
function view() {
    $item = $this->MyModel->findById($id);
    
    // Noindex if item is marked as non-indexable
    if (isset($item['MyModel']['no_index']) && $item['MyModel']['no_index']) {
        $this->noIndex = true;
    }
    
    $this->set(compact('item'));
}
```

## 🎯 Current Noindex Implementations

### Existing Patterns

1. **Individual Testimonial Pages** - All testimonial detail pages are noindexed
2. **Subscription Pages** - Subscription and confirmation pages are noindexed

### New Patterns (Recently Added)

1. **North America Destination** - `/destinations/north_america` is noindexed
2. **Holiday Testimonials** - `/destinations/*_holidays/testimonials` pages are noindexed

## 📋 Implementation Examples

### Example 1: Noindex Specific Destination

```php
// In DestinationsController->view()
if (isset($this->params['destination_slug'])) {
    $destinationSlug = $this->params['destination_slug'];
    
    // Noindex for north_america destination
    if ($destinationSlug === 'north_america') {
        $this->noIndex = true;
    }
}
```

### Example 2: Noindex URL Pattern

```php
// In TestimonialsController->index()
if (!empty($this->params['section']) && $this->params['section'] === 'destinations') {
    if (isset($this->params['destination_slug'])) {
        $destinationSlug = $this->params['destination_slug'];
        
        // Noindex for testimonials under destinations ending with _holidays
        if (preg_match('/^[a-z_]+_holidays$/', $destinationSlug)) {
            $this->noIndex = true;
        }
    }
}
```

### Example 3: Noindex All Actions in Controller

```php
// In any controller's beforeFilter()
function beforeFilter() {
    parent::beforeFilter();
    
    // Noindex all pages in this controller
    $this->noIndex = true;
}
```

## 🧪 Testing Your Implementation

### Manual Testing

1. **View page source** on the target URL
2. **Look for the meta tag**:
   - Development: `<meta name="robots" content="noindex, nofollow">`
   - Production: `<meta name="robots" content="noindex, follow">`
3. **Verify absence** on pages that should remain indexable

### Using cURL (Development)

```bash
# Test a page that should have noindex
curl -s https://bon-voyage.ddev.site/your-test-url | grep -i "robots"

# Should show: <meta name="robots" content="noindex, nofollow">
```

### Using the Test Script

Run the existing test script to verify implementation:

```bash
php test_noindex_implementation.php
```

## ⚠️ Important Considerations

### SEO Impact

- **Use "noindex, follow"** in production (automatic)
- **Allows link equity to flow** through noindexed pages
- **Prevents duplicate content** issues
- **Focuses crawl budget** on important pages

### When to Use Noindex

✅ **Good candidates for noindex:**
- Duplicate content pages
- Low-value utility pages
- Pagination pages beyond page 1
- Thank you/confirmation pages
- Search result pages
- Archive pages with thin content

❌ **Avoid noindexing:**
- Important landing pages
- Main category/section pages
- Pages with unique, valuable content
- Pages you want to rank in search results

### Performance Notes

- The `$noIndex` property is checked in `beforeRender()`
- Minimal performance impact
- Works with existing caching systems
- No database queries required for basic implementation

## 🔧 Troubleshooting

### Common Issues

1. **Noindex not appearing**: Check that `$this->noIndex = true` is set before `beforeRender()`
2. **Wrong robots directive**: Verify environment (dev vs production)
3. **Logic not triggering**: Add temporary debug output to verify conditions

### Debug Tips

```php
// Temporary debug in controller
if ($someCondition) {
    error_log("Setting noIndex for URL: " . $this->here);
    $this->noIndex = true;
}
```

---

**Last Updated:** December 2024  
**Implementation Method:** Meta tags with environment-aware directives  
**Production Behavior:** `noindex, follow` for marked pages, no restrictions for others
